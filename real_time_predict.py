#!/usr/bin/env python3
"""
实时预测脚本 - 用于真实交易环境
功能：
1. 从币安API获取最新的BTC/USDT 30分钟K线数据
2. 进行特征工程和预测
3. 记录预测结果到日志文件
4. 可以作为定时任务运行（建议每30分钟执行一次）

使用方法：
python real_time_predict.py

或设置crontab定时任务：
*/30 * * * * cd /ai-model && python real_time_predict.py
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import joblib
import os
import requests
import warnings
from datetime import datetime, timezone
import time
import ta

warnings.filterwarnings('ignore', category=UserWarning)

# --- 配置 ---
MODEL_DIR = "btc_reflection_model_v1"
PREDICTION_LOG_PATH = os.path.join(MODEL_DIR, "prediction_log.csv")
BINANCE_API_URL = "https://api.binance.com/api/v3/klines"

def create_features(df):
    """
    从原始K线数据创建丰富的特征 (与训练数据完全一致)
    包含所有训练时使用的技术指标
    """
    df_feat = df.copy()

    # 使用 ta 库计算技术指标，与训练数据保持一致
    try:
        # 动量指标
        df_feat['momentum_rsi'] = ta.momentum.RSIIndicator(df_feat['close']).rsi()

        # 趋势指标
        macd = ta.trend.MACD(df_feat['close'])
        df_feat['trend_macd'] = macd.macd()
        df_feat['trend_macd_signal'] = macd.macd_signal()
        df_feat['trend_macd_diff'] = macd.macd_diff()

        # 波动率指标 (布林带)
        bb = ta.volatility.BollingerBands(df_feat['close'])
        df_feat['volatility_bbm'] = bb.bollinger_mavg()
        df_feat['volatility_bbh'] = bb.bollinger_hband()
        df_feat['volatility_bbl'] = bb.bollinger_lband()
        df_feat['volatility_bbw'] = bb.bollinger_wband()
        df_feat['volatility_bbp'] = bb.bollinger_pband()

        # ADX 趋势强度指标
        adx = ta.trend.ADXIndicator(df_feat['high'], df_feat['low'], df_feat['close'])
        df_feat['trend_adx'] = adx.adx()
        df_feat['trend_adx_pos'] = adx.adx_pos()
        df_feat['trend_adx_neg'] = adx.adx_neg()

        # 成交量指标
        df_feat['volume_obv'] = ta.volume.OnBalanceVolumeIndicator(df_feat['close'], df_feat['volume']).on_balance_volume()

        # 基础价格特征
        df_feat['price_change'] = df_feat['close'].pct_change()
        df_feat['high_low_ratio'] = df_feat['high'] / df_feat['low']
        df_feat['body_ratio'] = abs(df_feat['close'] - df_feat['open']) / (df_feat['high'] - df_feat['low'] + 1e-9)

        # 多时间框架特征
        windows = [3, 5, 10, 20, 50, 100]
        for window in windows:
            df_feat[f'ma_{window}'] = df_feat['close'].rolling(window=window).mean()
            df_feat[f'price_ma_{window}_ratio'] = df_feat['close'] / (df_feat[f'ma_{window}'] + 1e-9)
            df_feat[f'momentum_{window}'] = df_feat['close'] / (df_feat['close'].shift(window) + 1e-9) - 1
            df_feat[f'volatility_{window}'] = df_feat['close'].rolling(window=window).std()

        # 额外的技术指标（与训练脚本一致）
        delta = df_feat['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-9)
        df_feat['rsi_14'] = 100 - (100 / (1 + rs))

        ema_fast = df_feat['close'].ewm(span=12).mean()
        ema_slow = df_feat['close'].ewm(span=26).mean()
        df_feat['macd'] = ema_fast - ema_slow
        df_feat['macd_signal'] = df_feat['macd'].ewm(span=9).mean()
        df_feat['macd_hist'] = df_feat['macd'] - df_feat['macd_signal']

        df_feat['volume_ma_20'] = df_feat['volume'].rolling(window=20).mean()
        df_feat['volume_ratio_20'] = df_feat['volume'] / (df_feat['volume_ma_20'] + 1e-9)
        df_feat['volume_spike'] = (df_feat['volume'] > df_feat['volume_ma_20'] * 2).astype(int)

        # 清理数据
        df_feat = df_feat.replace([np.inf, -np.inf], np.nan)
        df_feat.dropna(inplace=True)

        return df_feat

    except Exception as e:
        print(f"特征工程过程出错: {e}")
        return pd.DataFrame()

def fetch_latest_klines(symbol='BTCUSDT', interval='30m', limit=150):
    """
    从币安API获取最新的K线数据
    
    Args:
        symbol: 交易对，默认BTCUSDT
        interval: K线间隔，默认30m
        limit: 获取的K线数量，需要足够多以计算技术指标
    
    Returns:
        pandas.DataFrame: 包含OHLCV数据的DataFrame
    """
    try:
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        
        print(f"正在从币安API获取 {symbol} {interval} K线数据...")
        response = requests.get(BINANCE_API_URL, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        
        # 转换为DataFrame
        df = pd.DataFrame(data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])

        # 数据类型转换
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in ['open', 'high', 'low', 'close', 'volume', 'number_of_trades']:
            df[col] = df[col].astype(float)

        # 重命名和选择需要的列（包含 number_of_trades）
        df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume', 'number_of_trades']].copy()
        df.rename(columns={'timestamp': 'timestamp_utc'}, inplace=True)
        df.set_index('timestamp_utc', inplace=True)
        
        print(f"成功获取 {len(df)} 条K线数据，时间范围: {df.index[0]} 到 {df.index[-1]}")
        return df
        
    except requests.exceptions.RequestException as e:
        print(f"API请求失败: {e}")
        return None
    except Exception as e:
        print(f"数据处理失败: {e}")
        return None

def load_model_and_tools():
    """
    加载训练好的模型和预处理工具
    """
    try:
        model = joblib.load(os.path.join(MODEL_DIR, "lgbm_model.joblib"))
        scaler = joblib.load(os.path.join(MODEL_DIR, "scaler.joblib"))
        feature_columns = joblib.load(os.path.join(MODEL_DIR, "feature_columns.joblib"))
        
        print(f"成功加载模型和预处理工具")
        print(f"模型特征数量: {len(feature_columns)}")
        return model, scaler, feature_columns
        
    except FileNotFoundError as e:
        print(f"错误：找不到模型文件 - {e}")
        print(f"请确保已经运行 'train_initial_model.py' 训练初始模型")
        return None, None, None
    except Exception as e:
        print(f"加载模型失败: {e}")
        return None, None, None

def predict_latest_data(model, scaler, feature_columns, kline_data):
    """
    对最新的K线数据进行预测
    
    Args:
        model: 训练好的LightGBM模型
        scaler: 特征缩放器
        feature_columns: 特征列名列表
        kline_data: K线数据DataFrame
    
    Returns:
        dict: 预测结果
    """
    try:
        # 1. 特征工程
        df_featured = create_features(kline_data)
        
        if df_featured.empty:
            print("警告：特征工程后没有可用数据，可能是历史数据不足")
            return None
        
        # 2. 获取最新的一条数据进行预测
        latest_features = df_featured.iloc[-1:][feature_columns]
        latest_timestamp = df_featured.index[-1]
        
        # 3. 特征缩放
        X_scaled = scaler.transform(latest_features)
        
        # 4. 预测
        prediction = model.predict(X_scaled)[0]
        prediction_proba = model.predict_proba(X_scaled)[0, 1]  # 上涨概率
        
        # 5. 获取当前价格信息
        current_price = kline_data.iloc[-1]['close']
        
        result = {
            'timestamp': latest_timestamp,
            'current_price': current_price,
            'prediction': int(prediction),
            'prediction_proba_up': prediction_proba,
            'features': latest_features.iloc[0].to_dict()
        }
        
        return result
        
    except Exception as e:
        print(f"预测过程出错: {e}")
        return None

def log_prediction(prediction_result):
    """
    将预测结果记录到日志文件
    """
    try:
        # 准备日志数据
        log_data = {
            'timestamp': prediction_result['timestamp'],
            'current_price': prediction_result['current_price'],
            'prediction': prediction_result['prediction'],
            'prediction_proba_up': prediction_result['prediction_proba_up']
        }
        
        # 添加特征数据
        log_data.update(prediction_result['features'])
        
        # 转换为DataFrame
        log_df = pd.DataFrame([log_data])
        log_df.set_index('timestamp', inplace=True)
        
        # 写入文件
        if os.path.exists(PREDICTION_LOG_PATH):
            log_df.to_csv(PREDICTION_LOG_PATH, mode='a', header=False)
        else:
            log_df.to_csv(PREDICTION_LOG_PATH)
        
        print(f"预测结果已记录到: {PREDICTION_LOG_PATH}")
        
    except Exception as e:
        print(f"记录预测结果失败: {e}")

def main():
    """
    主函数：执行实时预测流程
    """
    print(f"\n=== BTC价格预测 - {datetime.now()} ===")
    
    # 1. 加载模型和工具
    model, scaler, feature_columns = load_model_and_tools()
    if model is None:
        print("无法加载模型，退出程序")
        return
    
    # 2. 获取最新K线数据
    kline_data = fetch_latest_klines()
    if kline_data is None:
        print("无法获取K线数据，退出程序")
        return
    
    # 3. 进行预测
    prediction_result = predict_latest_data(model, scaler, feature_columns, kline_data)
    if prediction_result is None:
        print("预测失败，退出程序")
        return
    
    # 4. 显示预测结果
    print(f"\n--- 预测结果 ---")
    print(f"时间: {prediction_result['timestamp']}")
    print(f"当前价格: ${prediction_result['current_price']:,.2f}")
    print(f"预测方向: {'上涨' if prediction_result['prediction'] == 1 else '下跌'}")
    print(f"上涨概率: {prediction_result['prediction_proba_up']:.2%}")
    
    # 5. 记录预测结果
    log_prediction(prediction_result)
    
    print(f"预测完成！")

if __name__ == "__main__":
    main()
