
import pandas as pd
import numpy as np
import lightgbm as lgb
import joblib
import os
import warnings

warnings.filterwarnings('ignore', category=UserWarning)

# --- 配置 ---
MODEL_DIR = "btc_reflection_model_v1"
FILE_PATH = "BTCUSDT_30m_2020-01-01_to_2024-12-31_minimal_features.csv" # 确保此文件存在
PREDICTION_WINDOW = 1
PREDICTION_LOG_PATH = os.path.join(MODEL_DIR, "prediction_log.csv")
REFLECTION_DATA_PATH = os.path.join(MODEL_DIR, "reflection_data.csv")

# --- 核心函数 ---

def create_features(df):
    """
    从原始K线数据创建丰富的特征 (与训练脚本完全一致)
    """
    df_feat = df.copy()
    
    # 基础价格特征
    df_feat['price_change'] = df_feat['close'].pct_change()
    df_feat['high_low_ratio'] = df_feat['high'] / df_feat['low']
    df_feat['body_ratio'] = abs(df_feat['close'] - df_feat['open']) / (df_feat['high'] - df_feat['low'] + 1e-9)

    # 多时间框架特征
    windows = [3, 5, 10, 20, 50, 100]
    for window in windows:
        df_feat[f'ma_{window}'] = df_feat['close'].rolling(window=window).mean()
        df_feat[f'price_ma_{window}_ratio'] = df_feat['close'] / (df_feat[f'ma_{window}'] + 1e-9)
        df_feat[f'momentum_{window}'] = df_feat['close'] / (df_feat['close'].shift(window) + 1e-9) - 1
        df_feat[f'volatility_{window}'] = df_feat['close'].rolling(window=window).std()

    # 技术指标
    delta = df_feat['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / (loss + 1e-9)
    df_feat['rsi_14'] = 100 - (100 / (1 + rs))

    ema_fast = df_feat['close'].ewm(span=12).mean()
    ema_slow = df_feat['close'].ewm(span=26).mean()
    df_feat['macd'] = ema_fast - ema_slow
    df_feat['macd_signal'] = df_feat['macd'].ewm(span=9).mean()
    df_feat['macd_hist'] = df_feat['macd'] - df_feat['macd_signal']

    df_feat['volume_ma_20'] = df_feat['volume'].rolling(window=20).mean()
    df_feat['volume_ratio_20'] = df_feat['volume'] / (df_feat['volume_ma_20'] + 1e-9)
    df_feat['volume_spike'] = (df_feat['volume'] > df_feat['volume_ma_20'] * 2).astype(int)

    df_feat = df_feat.replace([np.inf, -np.inf], np.nan)
    df_feat.dropna(inplace=True)
    
    return df_feat

def prepare_data_for_reflection(file_path, prediction_window):
    """
    【优化点】一个轻量级的数据准备函数，仅用于获取验证所需的'actual_target'，避免重复计算所有特征。
    """
    print("\n--- (验证) 高效加载历史数据以获取真实目标 ---")
    df = pd.read_csv(file_path)
    df['timestamp_utc'] = pd.to_datetime(df['timestamp_utc'])
    df.set_index('timestamp_utc', inplace=True)
    
    # 计算未来回报率
    df['future_return'] = df['close'].pct_change(periods=-prediction_window).shift(-prediction_window)
    
    # 使用与训练时相同的逻辑计算动态阈值
    threshold = df['future_return'].rolling(window=2000, min_periods=500).std().mean() * 0.5
    print(f"使用动态阈值: ±{threshold:.6f} 来验证预测")
    
    # 定义真实目标
    df['actual_target'] = 0
    df.loc[df['future_return'] > threshold, 'actual_target'] = 1
    
    # 只返回包含真实目标的Series，以时间为索引
    return df['actual_target']

# --- 加载模型和工具 (程序启动时执行一次) ---
print("--- 加载已训练的模型和预处理器 ---")
try:
    model = joblib.load(os.path.join(MODEL_DIR, "lgbm_model.joblib"))
    scaler = joblib.load(os.path.join(MODEL_DIR, "scaler.joblib"))
    feature_columns = joblib.load(os.path.join(MODEL_DIR, "feature_columns.joblib"))
except FileNotFoundError:
    print(f"错误：在目录 '{MODEL_DIR}' 中找不到初始模型。请先成功运行 'train_initial_model.py'。")
    exit()

def predict_new_data(new_ohlcv_df):
    """
    对新的K线数据进行预测并记录。
    【修正点】此函数现在能处理原始OHLCV数据，因为它内部调用了create_features。
    """
    print("\n--- 1. 对新数据进行预测 ---")
    
    # 1. 从原始OHLCV数据创建特征
    df_new_featured = create_features(new_ohlcv_df)
    
    if df_new_featured.empty:
        print("特征工程后没有可供预测的新数据 (可能是因为窗口期数据不足)。")
        return

    # 确保所有需要的特征列都存在
    if not all(col in df_new_featured.columns for col in feature_columns):
        print("错误：新数据创建的特征与模型训练时的特征不匹配。")
        return

    # 2. 准备预测数据
    X_new = df_new_featured[feature_columns]
    X_new_scaled = scaler.transform(X_new)
    
    # 3. 预测
    predictions = model.predict(X_new_scaled)
    probabilities = model.predict_proba(X_new_scaled)[:, 1] # 获取上涨的概率

    # 4. 记录预测结果
    log_df = X_new.copy()
    log_df['prediction'] = predictions
    log_df['prediction_proba_up'] = probabilities
    
    # 【修正点】保存日志时要包含索引(时间戳)，以便后续匹配
    if os.path.exists(PREDICTION_LOG_PATH):
        log_df.to_csv(PREDICTION_LOG_PATH, mode='a', header=False, index=True)
    else:
        log_df.to_csv(PREDICTION_LOG_PATH, index=True)
        
    print(f"成功预测并记录了 {len(log_df)} 条新数据。")

def retrain_with_reflection(historical_data_path, prediction_window):
    """
    【已修正】核心的“反思”与“学习”环节。
    """
    print("\n--- 2. 开始反思与重训练循环 ---")

    # 1. 加载预测日志
    if not os.path.exists(PREDICTION_LOG_PATH):
        print("没有预测日志，无法进行反思学习。")
        return
        
    log_df = pd.read_csv(PREDICTION_LOG_PATH, index_col=0, parse_dates=True)
    log_df = log_df[~log_df.index.duplicated(keep='last')] # 保险起见，去重

    # 2. 【优化点】高效验证预测结果 (获取真实标签)
    actual_targets = prepare_data_for_reflection(historical_data_path, prediction_window)
    
    # 合并预测和真实结果
    reflection_df = log_df.join(actual_targets, how='inner')
    reflection_df.dropna(subset=['actual_target'], inplace=True)
    reflection_df['actual_target'] = reflection_df['actual_target'].astype(int)
    
    if reflection_df.empty:
        print("无法将预测日志与实际结果对齐，请检查时间戳。跳过重训练。")
        os.remove(PREDICTION_LOG_PATH) # 清理旧日志
        return

    reflection_df['is_correct'] = (reflection_df['prediction'] == reflection_df['actual_target']).astype(int)

    print(f"验证了 {len(reflection_df)} 条预测。")
    print(f"其中，正确率: {reflection_df['is_correct'].mean():.2%}")

    # 3. "反思" - 识别错误样本
    error_samples = reflection_df[reflection_df['is_correct'] == 0]
    print(f"识别出 {len(error_samples)} 个错误样本，这些是宝贵的学习资料。")
    
    if len(error_samples) < 10: # 如果错误样本太少，也跳过训练，避免过拟合
        print("错误样本过少，本次不进行重训练以保持模型稳定。")
        # 清理日志，为下个周期做准备
        os.remove(PREDICTION_LOG_PATH)
        return

    # 4. "学习" - 准备重训练数据
    X_new_verified = reflection_df[feature_columns]
    y_new_verified = reflection_df['actual_target']
    
    # 关键步骤：定义样本权重。让模型更关注它犯错的地方。
    sample_weights = reflection_df['is_correct'].apply(lambda x: 1 if x == 1 else 3).values
    
    X_new_scaled = scaler.transform(X_new_verified)
    
    # 创建 LightGBM 数据集
    train_data = lgb.Dataset(X_new_scaled, label=y_new_verified, weight=sample_weights)
    
    print("\n--- 3. 使用反思数据进行增量训练 ---")
    
    # 【核心修正点】使用正确的 lgb.train API
    params = model.get_params()
    params['metric'] = 'binary_logloss' # API 要求在 params 中定义 metric
    params.pop('n_estimators', None)
    params.pop('importance_type', None)
    
    # 使用 `lgb.train` 进行增量学习
    new_booster = lgb.train(
        params=params,
        train_set=train_data,
        init_model=model,                  # 从旧模型(LGBMClassifier实例)继续训练
        num_boost_round=200,               # 再训练最多200轮
        valid_sets=[train_data],           # 【修正】使用 valid_sets
        callbacks=[lgb.early_stopping(stopping_rounds=50, verbose=False)] # 【修正】正确的早停回调
    )

    # 5. 保存更新后的模型
    # 将训练好的新Booster更新回我们的LGBMClassifier实例中
    model.booster_ = new_booster

    new_model_iteration = model.booster_.current_iteration()
    new_model_path = os.path.join(MODEL_DIR, f"lgbm_model_iter_{new_model_iteration}.joblib")
    joblib.dump(model, new_model_path)
    joblib.dump(model, os.path.join(MODEL_DIR, "lgbm_model.joblib")) # 覆盖旧模型，作为最新版
    print(f"模型已更新并保存。最新版本迭代次数: {new_model_iteration}。路径: {new_model_path}")

    # 6. 清理
    # 将已学习过的数据存档，并清空日志文件
    if os.path.exists(REFLECTION_DATA_PATH):
        reflection_df.to_csv(REFLECTION_DATA_PATH, mode='a', header=False)
    else:
        reflection_df.to_csv(REFLECTION_DATA_PATH)
    os.remove(PREDICTION_LOG_PATH) # 【修正】正确的变量名
    print("预测日志已清理，准备下一个学习周期。")

# --- 模拟工作流程 (主程序入口) ---
if __name__ == '__main__':
    print("\n====== 开始模拟一个学习周期 ======")
    
    # 1. 模拟获取新数据
    # 在真实场景中，你会从API获取实时K线
    # 这里我们从完整历史数据中截取一部分作为“新”数据
    print("\n--- (模拟) 获取新数据 ---")
    full_ohlcv_df = pd.read_csv(FILE_PATH)
    
    # 为了让 create_features 能正确计算所有指标 (如ma_100)，需要包含新数据之前的至少100条历史数据
    # 假设最后1000条是“新产生”的，我们需要预测它们
    new_data_chunk = full_ohlcv_df.tail(1000 + 100).copy()
    new_data_chunk['timestamp_utc'] = pd.to_datetime(new_data_chunk['timestamp_utc'])
    new_data_chunk.set_index('timestamp_utc', inplace=True)
    print(f"获取了 {len(new_data_chunk)} 条数据用于生成特征和预测。")
    
    # 2. 第一步：预测新数据并记录
    predict_new_data(new_data_chunk)
    
    # 3. 第二步：模拟时间流逝，我们获得了真实结果，开始反思和学习
    # 在真实应用中，这会是一个独立的、定时执行的任务 (例如每天凌晨执行)
    print("\n--- (模拟) 时间流逝，开始每日学习任务 ---")
    retrain_with_reflection(FILE_PATH, PREDICTION_WINDOW)
    
    print("\n====== 学习周期完成 ======")
