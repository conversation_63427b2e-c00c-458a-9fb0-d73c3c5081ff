import pandas as pd
import numpy as np
import lightgbm as lgb
import joblib
import os
import warnings

warnings.filterwarnings('ignore', category=UserWarning)

# --- 配置 ---
MODEL_DIR = "btc_reflection_model_v1"
FILE_PATH = "BTCUSDT_30m_2020-01-01_to_2024-12-31_minimal_features.csv"
PREDICTION_WINDOW = 1
PREDICTION_LOG_PATH = os.path.join(MODEL_DIR, "prediction_log.csv")
REFLECTION_DATA_PATH = os.path.join(MODEL_DIR, "reflection_data.csv")

# --- 核心函数 (保持不变) ---
def create_features(df):
    df_feat = df.copy()
    df_feat['price_change'] = df_feat['close'].pct_change()
    df_feat['high_low_ratio'] = df_feat['high'] / df_feat['low']
    df_feat['body_ratio'] = abs(df_feat['close'] - df_feat['open']) / (df_feat['high'] - df_feat['low'] + 1e-9)
    windows = [3, 5, 10, 20, 50, 100]
    for window in windows:
        df_feat[f'ma_{window}'] = df_feat['close'].rolling(window=window).mean()
        df_feat[f'price_ma_{window}_ratio'] = df_feat['close'] / (df_feat[f'ma_{window}'] + 1e-9)
        df_feat[f'momentum_{window}'] = df_feat['close'] / (df_feat['close'].shift(window) + 1e-9) - 1
        df_feat[f'volatility_{window}'] = df_feat['close'].rolling(window=window).std()
    delta = df_feat['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / (loss + 1e-9)
    df_feat['rsi_14'] = 100 - (100 / (1 + rs))
    ema_fast = df_feat['close'].ewm(span=12).mean()
    ema_slow = df_feat['close'].ewm(span=26).mean()
    df_feat['macd'] = ema_fast - ema_slow
    df_feat['macd_signal'] = df_feat['macd'].ewm(span=9).mean()
    df_feat['macd_hist'] = df_feat['macd'] - df_feat['macd_signal']
    df_feat['volume_ma_20'] = df_feat['volume'].rolling(window=20).mean()
    df_feat['volume_ratio_20'] = df_feat['volume'] / (df_feat['volume_ma_20'] + 1e-9)
    df_feat['volume_spike'] = (df_feat['volume'] > df_feat['volume_ma_20'] * 2).astype(int)
    df_feat = df_feat.replace([np.inf, -np.inf], np.nan)
    df_feat.dropna(inplace=True)
    return df_feat

def prepare_data_for_reflection(file_path, prediction_window):
    print("\n--- (验证) 高效加载历史数据以获取真实目标 ---")
    df = pd.read_csv(file_path)
    df['timestamp_utc'] = pd.to_datetime(df['timestamp_utc'])
    df.set_index('timestamp_utc', inplace=True)
    df['future_return'] = df['close'].pct_change(periods=-prediction_window).shift(-prediction_window)
    threshold = df['future_return'].rolling(window=2000, min_periods=500).std().mean() * 0.5
    print(f"使用动态阈值: ±{threshold:.6f} 来验证预测")
    df['actual_target'] = 0
    df.loc[df['future_return'] > threshold, 'actual_target'] = 1
    return df['actual_target']

# --- 加载模型和工具 (程序启动时执行一次) ---
print("--- 加载已训练的模型和预处理器 ---")
try:
    model = joblib.load(os.path.join(MODEL_DIR, "lgbm_model.joblib"))
    scaler = joblib.load(os.path.join(MODEL_DIR, "scaler.joblib"))
    feature_columns = joblib.load(os.path.join(MODEL_DIR, "feature_columns.joblib"))
except FileNotFoundError:
    print(f"错误：在目录 '{MODEL_DIR}' 中找不到初始模型。请先成功运行 'train_initial_model.py'。")
    exit()

def predict_new_data(new_ohlcv_df):
    print("\n--- 1. 对新数据进行预测 ---")
    df_new_featured = create_features(new_ohlcv_df)
    if df_new_featured.empty:
        print("特征工程后没有可供预测的新数据 (可能是因为窗口期数据不足)。")
        return
    if not all(col in df_new_featured.columns for col in feature_columns):
        print("错误：新数据创建的特征与模型训练时的特征不匹配。")
        return
    X_new = df_new_featured[feature_columns]
    X_new_scaled = scaler.transform(X_new)
    predictions = model.predict(X_new_scaled)
    probabilities = model.predict_proba(X_new_scaled)[:, 1]
    log_df = X_new.copy()
    log_df['prediction'] = predictions
    log_df['prediction_proba_up'] = probabilities
    if os.path.exists(PREDICTION_LOG_PATH):
        log_df.to_csv(PREDICTION_LOG_PATH, mode='a', header=False, index=True)
    else:
        log_df.to_csv(PREDICTION_LOG_PATH, index=True)
    print(f"成功预测并记录了 {len(log_df)} 条新数据。")

def retrain_with_reflection(historical_data_path, prediction_window):
    """
    【已优化】核心的"反思"与"学习"环节。
    """
    global model # 声明我们要修改全局的model变量
    print("\n--- 2. 开始反思与重训练循环 ---")
    if not os.path.exists(PREDICTION_LOG_PATH):
        print("没有预测日志，无法进行反思学习。")
        return
    log_df = pd.read_csv(PREDICTION_LOG_PATH, index_col=0, parse_dates=True)
    log_df = log_df[~log_df.index.duplicated(keep='last')]
    actual_targets = prepare_data_for_reflection(historical_data_path, prediction_window)
    reflection_df = log_df.join(actual_targets, how='inner')
    reflection_df.dropna(subset=['actual_target'], inplace=True)
    if reflection_df.empty:
        print("无法将预测日志与实际结果对齐，请检查时间戳。跳过重训练。")
        os.remove(PREDICTION_LOG_PATH)
        return
    reflection_df['actual_target'] = reflection_df['actual_target'].astype(int)
    reflection_df['is_correct'] = (reflection_df['prediction'] == reflection_df['actual_target']).astype(int)
    print(f"验证了 {len(reflection_df)} 条预测。")
    print(f"其中，正确率: {reflection_df['is_correct'].mean():.2%}")
    error_samples = reflection_df[reflection_df['is_correct'] == 0]
    print(f"识别出 {len(error_samples)} 个错误样本，这些是宝贵的学习资料。")
    if len(error_samples) < 10:
        print("错误样本过少，本次不进行重训练以保持模型稳定。")
        os.remove(PREDICTION_LOG_PATH)
        return
    X_new_verified = reflection_df[feature_columns]
    y_new_verified = reflection_df['actual_target']
    sample_weights = reflection_df['is_correct'].apply(lambda x: 1 if x == 1 else 3).values
    X_new_scaled = scaler.transform(X_new_verified)
    train_data = lgb.Dataset(X_new_scaled, label=y_new_verified, weight=sample_weights)
    print("\n--- 3. 使用反思数据进行增量训练 ---")
    params = model.get_params()
    params['metric'] = 'binary_logloss'
    params.pop('n_estimators', None)
    params.pop('importance_type', None)
    
    new_booster = lgb.train(
        params=params,
        train_set=train_data,
        init_model=model,
        num_boost_round=200,
        valid_sets=[train_data],
        callbacks=[lgb.early_stopping(stopping_rounds=50, verbose=False)]
    )

    # 5. 保存更新后的模型
    # 【优化点】直接将训练好的新Booster赋回给Scikit-Learn包装器即可，无需存取临时文件
    model.booster_ = new_booster
    # model._Booster = new_booster # 或者使用这个私有属性，两者效果通常一样
    model._n_features_in = X_new_scaled.shape[1] # 更新特征数量
    model.fitted_ = True # 标记模型为已拟合

    new_model_iteration = model.booster_.current_iteration()
    new_model_path = os.path.join(MODEL_DIR, f"lgbm_model_iter_{new_model_iteration}.joblib")
    joblib.dump(model, new_model_path)
    joblib.dump(model, os.path.join(MODEL_DIR, "lgbm_model.joblib"))
    print(f"模型已更新并保存。最新版本迭代次数: {new_model_iteration}。路径: {new_model_path}")

    # 6. 清理
    if os.path.exists(REFLECTION_DATA_PATH):
        reflection_df.to_csv(REFLECTION_DATA_PATH, mode='a', header=False)
    else:
        reflection_df.to_csv(REFLECTION_DATA_PATH)
    os.remove(PREDICTION_LOG_PATH)
    print("预测日志已清理，准备下一个学习周期。")

# --- 模拟工作流程 (主程序入口，保持不变) ---
if __name__ == '__main__':
    print("\n====== 开始模拟一个学习周期 ======")
    print("\n--- (模拟) 获取用于预测的实时数据 ---")
    full_ohlcv_df = pd.read_csv(FILE_PATH)
    split_point = len(full_ohlcv_df) - 1000
    known_history_df = full_ohlcv_df.iloc[:split_point]
    future_unseen_df = full_ohlcv_df.iloc[split_point:]
    data_for_prediction_engine = pd.concat([
        known_history_df.tail(100), 
        future_unseen_df
    ])
    data_for_prediction_engine['timestamp_utc'] = pd.to_datetime(data_for_prediction_engine['timestamp_utc'])
    data_for_prediction_engine.set_index('timestamp_utc', inplace=True)
    print(f"预测引擎接收 {len(data_for_prediction_engine)} 条数据，将对其中 {len(future_unseen_df)} 条进行预测。")
    predict_new_data(data_for_prediction_engine)
    print("\n--- (模拟) 时间流逝，开始每日学习任务 ---")
    retrain_with_reflection(FILE_PATH, PREDICTION_WINDOW)
    print("\n====== 学习周期完成 ======")
    print("\n【重要提示】: 如果初始模型训练时存在数据泄露，您需要删除 'btc_reflection_model_v1' 目录，")
    print("然后重新运行 'train_initial_model.py'，再运行本脚本。")

